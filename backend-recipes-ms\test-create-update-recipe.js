/**
 * Simple test script to verify the createRecipeBasicInfo function
 * handles both create and update operations correctly
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:8028'; // Adjust based on your server configuration
const API_ENDPOINT = '/api/v1/recipes/basic-info';

// Mock authentication token - replace with a valid token for your environment
const AUTH_TOKEN = 'your-auth-token-here';

// Test data for creating a recipe
const createRecipeData = {
  recipe_title: "Test Recipe for Create/Update",
  recipe_public_title: "Public Test Recipe",
  recipe_description: "This is a test recipe to verify create/update functionality",
  recipe_preparation_time: 15,
  recipe_cook_time: 30,
  has_recipe_public_visibility: true,
  has_recipe_private_visibility: false,
  recipe_status: "draft",
  recipe_complexity_level: "medium",
  categories: [1, 2], // Adjust based on your category IDs
  dietary_attributes: [1, 3] // Adjust based on your dietary attribute IDs
};

// Test data for updating a recipe (will be populated after create)
const updateRecipeData = {
  recipe_title: "Updated Test Recipe",
  recipe_public_title: "Updated Public Test Recipe",
  recipe_description: "This recipe has been updated to test the update functionality",
  recipe_preparation_time: 20,
  recipe_cook_time: 45,
  has_recipe_public_visibility: false,
  has_recipe_private_visibility: true,
  recipe_status: "draft",
  recipe_complexity_level: "hard",
  categories: [2, 3], // Different categories
  dietary_attributes: [2, 4] // Different dietary attributes
};

async function makeRequest(data, description) {
  try {
    console.log(`\n=== ${description} ===`);
    console.log('Request data:', JSON.stringify(data, null, 2));
    
    const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, data, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
}

async function testCreateUpdateRecipe() {
  try {
    // Test 1: Create a new recipe (without recipe_id)
    console.log('🧪 Testing CREATE operation (without recipe_id)...');
    const createResponse = await makeRequest(createRecipeData, 'CREATE Recipe');
    
    if (!createResponse.status || !createResponse.data?.recipe_id) {
      throw new Error('Create operation failed or did not return recipe_id');
    }
    
    const recipeId = createResponse.data.recipe_id;
    console.log(`✅ Recipe created successfully with ID: ${recipeId}`);
    
    // Test 2: Update the created recipe (with recipe_id)
    console.log('\n🧪 Testing UPDATE operation (with recipe_id)...');
    const updateData = {
      ...updateRecipeData,
      recipe_id: recipeId
    };
    
    const updateResponse = await makeRequest(updateData, 'UPDATE Recipe');
    
    if (!updateResponse.status || updateResponse.data?.recipe_id !== recipeId) {
      throw new Error('Update operation failed or returned incorrect recipe_id');
    }
    
    console.log(`✅ Recipe updated successfully with ID: ${recipeId}`);
    
    // Test 3: Verify backward compatibility - create another recipe without recipe_id
    console.log('\n🧪 Testing backward compatibility (create without recipe_id)...');
    const backwardCompatData = {
      ...createRecipeData,
      recipe_title: "Backward Compatibility Test Recipe"
    };
    
    const backwardResponse = await makeRequest(backwardCompatData, 'BACKWARD COMPATIBILITY Test');
    
    if (!backwardResponse.status || !backwardResponse.data?.recipe_id) {
      throw new Error('Backward compatibility test failed');
    }
    
    console.log(`✅ Backward compatibility verified with new recipe ID: ${backwardResponse.data.recipe_id}`);
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('1. ✅ CREATE operation works correctly');
    console.log('2. ✅ UPDATE operation works correctly');
    console.log('3. ✅ Backward compatibility maintained');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Instructions for running the test
console.log('📝 Instructions for running this test:');
console.log('1. Make sure your Recipe MS server is running on the configured port');
console.log('2. Update the AUTH_TOKEN variable with a valid authentication token');
console.log('3. Update category and dietary attribute IDs to match your database');
console.log('4. Run: node test-create-update-recipe.js');
console.log('\n🚀 Starting tests...');

// Uncomment the line below to run the test
// testCreateUpdateRecipe();

module.exports = { testCreateUpdateRecipe };
