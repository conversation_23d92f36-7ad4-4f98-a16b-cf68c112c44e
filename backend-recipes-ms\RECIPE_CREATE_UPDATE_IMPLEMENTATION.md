# Recipe Create/Update Implementation

## Overview

The `createRecipeBasicInfo` function in `backend-recipes-ms/src/controller/recipe-batch.controller.ts` has been modified to handle both **create** and **update** operations based on the presence of the `recipe_id` parameter in the request.

## Implementation Details

### Key Changes Made

1. **Conditional Logic**: Added logic to detect whether this is a create or update operation based on the presence of `recipe_id`
2. **Recipe Validation**: For updates, validates that the recipe exists and the user has access to it
3. **Slug Management**: Smart slug generation that only regenerates when the title changes during updates
4. **Categories Handling**: <PERSON><PERSON>ly handles category updates by removing existing and adding new ones
5. **Dietary Attributes**: Handles dietary attribute updates by removing existing and adding new ones
6. **History Tracking**: Creates appropriate history entries for both create and update operations
7. **Response Handling**: Returns appropriate HTTP status codes (201 for create, 200 for update)

### API Behavior

#### Create Operation (Existing Behavior)
- **Trigger**: When `recipe_id` is NOT provided in the request
- **Action**: Creates a new recipe with all provided information
- **Response**: HTTP 201 with new recipe ID and slug
- **Backward Compatibility**: ✅ Fully maintained

#### Update Operation (New Behavior)
- **Trigger**: When `recipe_id` IS provided in the request
- **Action**: Updates existing recipe with provided information
- **Validation**: Ensures recipe exists and user has access
- **Response**: HTTP 200 with updated recipe ID and slug

### Request Examples

#### Create Recipe (No recipe_id)
```json
{
  "recipe_title": "New Recipe",
  "recipe_description": "A delicious new recipe",
  "recipe_preparation_time": 15,
  "recipe_cook_time": 30,
  "categories": [1, 2],
  "dietary_attributes": [1, 3]
}
```

#### Update Recipe (With recipe_id)
```json
{
  "recipe_id": 123,
  "recipe_title": "Updated Recipe",
  "recipe_description": "An updated delicious recipe",
  "recipe_preparation_time": 20,
  "recipe_cook_time": 35,
  "categories": [2, 3],
  "dietary_attributes": [2, 4]
}
```

### Response Format

Both operations return the same response format:

```json
{
  "status": true,
  "message": "Recipe basic information saved/updated successfully",
  "data": {
    "recipe_id": 123,
    "recipe_slug": "recipe-slug"
  }
}
```

## Technical Implementation

### Validation
- The existing `validateBasicInfoBatch()` validator already supports `recipe_id` as an optional field
- No changes to validation were required

### Database Operations
- **Create**: Uses `Recipe.create()` with full recipe data including timestamps
- **Update**: Uses `Recipe.update()` with only the fields that can be updated
- **Categories**: Uses `destroy()` + `bulkCreate()` pattern for updates
- **Dietary Attributes**: Uses `destroy()` + `bulkCreate()` pattern for updates

### Transaction Management
- All operations are wrapped in database transactions
- Rollback occurs automatically if any operation fails
- Ensures data consistency across all related tables

### Slug Handling
- **Create**: Always generates a new unique slug
- **Update**: Only regenerates slug if the title has changed
- **Uniqueness**: Excludes current recipe from slug uniqueness check during updates

### History Tracking
- **Create**: Records `RecipeHistoryAction.created`
- **Update**: Records `RecipeHistoryAction.updated`
- Includes appropriate descriptions for both operations

## Security Considerations

1. **Access Control**: Updates validate that the user has access to the recipe through `validateRecipeAccess()`
2. **Organization Isolation**: Ensures recipes can only be updated within the same organization
3. **Input Sanitization**: All inputs are sanitized using `ValidationHelper.sanitizeInput()`
4. **Transaction Safety**: All operations are atomic through database transactions

## Testing

A test script has been provided at `backend-recipes-ms/test-create-update-recipe.js` to verify:

1. ✅ Create operation works correctly (without recipe_id)
2. ✅ Update operation works correctly (with recipe_id)
3. ✅ Backward compatibility is maintained
4. ✅ Proper HTTP status codes are returned
5. ✅ Response format is consistent

### Running Tests

1. Ensure the Recipe MS server is running
2. Update the `AUTH_TOKEN` in the test file with a valid token
3. Update category and dietary attribute IDs to match your database
4. Run: `node test-create-update-recipe.js`

## Backward Compatibility

✅ **Fully Maintained**: Existing API calls without `recipe_id` continue to work exactly as before, creating new recipes with HTTP 201 responses.

## Error Handling

- **Recipe Not Found**: Returns HTTP 404 when trying to update a non-existent recipe
- **Access Denied**: Returns HTTP 404 when user doesn't have access to the recipe
- **Validation Errors**: Returns HTTP 400 for invalid input data
- **Server Errors**: Returns HTTP 500 for unexpected errors with proper error messages

## Production Considerations

- **Minimal Risk**: Changes are additive and maintain full backward compatibility
- **Performance**: No performance impact on existing create operations
- **Monitoring**: Consider monitoring update vs create operation ratios
- **Logging**: All operations are logged through the existing history system
